import * as cdk from "aws-cdk-lib";
import {BlockPublicAccess, Bucket} from "aws-cdk-lib/aws-s3";
import {Certificate, ICertificate} from "aws-cdk-lib/aws-certificatemanager";
import {
    CachedMethods,
    CachePolicy,
    Distribution,
    HttpVersion, OriginAccessIdentity,
    OriginRequestPolicy,
    PriceClass,
    ViewerProtocolPolicy
} from "aws-cdk-lib/aws-cloudfront";
import {S3BucketOrigin} from "aws-cdk-lib/aws-cloudfront-origins";
import * as s3deploy from 'aws-cdk-lib/aws-s3-deployment';
import * as path from "path";
import {DockerImage, Duration} from "aws-cdk-lib";
import {Construct} from "constructs";
import {CanonicalUserPrincipal, PolicyStatement} from "aws-cdk-lib/aws-iam";


interface LogProps {
    bucket: Bucket,
    prefix?: string,
}

interface CloudfrontS3Props {
    domainNames: string[],
    certificateArn: string,
    bucketName: string,
    certificate?: ICertificate,

    logProps?: LogProps,
    codeLocalPath?: string
    isSinglePageApplication?: boolean,
}

export class CloudfrontS3 extends Construct {
    distribution: Distribution; // 暴露内部资源给外部调用。将这俩实例保存为类的公共属性
    bucket: Bucket;

    constructor(scope: Construct, id: string, props: CloudfrontS3Props) {
        super(scope, id);

        // create website bucket 关闭公网访问，开启版本控制。
        const bucket = new Bucket(this, 'bucket', {
            bucketName: props.bucketName,
            blockPublicAccess: BlockPublicAccess.BLOCK_ALL,
            versioned: true,
            publicReadAccess: false,
        });
        // 创建cloudfront OAI 来源访问标识
        const cloudfrontOAI = new OriginAccessIdentity(this, "CloudfrontOAI", {
            comment: `Cloudfront OAI for ${props.domainNames}`,
        });

        // allow cloudfront to access bucket
        bucket.grantRead(cloudfrontOAI);    
        bucket.addToResourcePolicy(            // allow cloudfront to access bucket
            new PolicyStatement({
                actions: ["s3:GetObject"],
                resources: [bucket.arnForObjects("*")],
                principals: [
                    new CanonicalUserPrincipal(
                        cloudfrontOAI.cloudFrontOriginAccessIdentityS3CanonicalUserId
                    ),
                ],
            })
        );

        // cloudfront log bucket 
        const logBucket = new Bucket(this, 'log-bucket', {
            blockPublicAccess: BlockPublicAccess.BLOCK_ALL,
            lifecycleRules: [{
                expiration: Duration.days(30)
            }]
        })

        if (!props.logProps) {
            props.logProps = {
                bucket: logBucket,
                prefix: 'cf'
            }
        }

        // cloudfront
        if (!props.certificate) {
            props.certificate = Certificate.fromCertificateArn(this, 'cer',
                props.certificateArn);
        }

        const cachePolicy = new CachePolicy(this, 'staticCP', {
            enableAcceptEncodingGzip: true,
            enableAcceptEncodingBrotli: true
        });
        const originRequestPolicy = new OriginRequestPolicy(this, 'staticORC', {});

        const distribution = new Distribution(this, 'cf-web', {
            defaultBehavior: {
                origin: S3BucketOrigin.withOriginAccessIdentity(bucket, {originAccessIdentity: cloudfrontOAI}),
                viewerProtocolPolicy: ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
                cachedMethods: CachedMethods.CACHE_GET_HEAD,
                originRequestPolicy: OriginRequestPolicy.CORS_S3_ORIGIN
            },

            // errorResponses: [{
            //     httpStatus: 403,
            //     responseHttpStatus: 200,
            //     responsePagePath: '/index.html'
            // }, {
            //     httpStatus: 404,
            //     responseHttpStatus: 200,
            //     responsePagePath: '/index.html'
            // }],

            httpVersion: HttpVersion.HTTP1_1,
            priceClass: PriceClass.PRICE_CLASS_ALL,

            // logBucket: props.logProps.bucket,
            // logFilePrefix: props.logProps.prefix,

            domainNames: props.domainNames,
            certificate: props.certificate,

            defaultRootObject: 'index.html'
        });

        this.distribution = distribution;
        this.bucket = bucket;

        // s3 deployment
        new s3deploy.BucketDeployment(this, 'DeployResources', {
            sources: [s3deploy.Source.asset(path.join(__dirname, '../app'))],
            destinationBucket: bucket,
            distribution,
            // memoryLimit: 1024,
            // ephemeralStorageSize: Size.gibibytes(1),
            // contentType: "text/plain",
            distributionPaths: ["/*"],
        });
    }
}

