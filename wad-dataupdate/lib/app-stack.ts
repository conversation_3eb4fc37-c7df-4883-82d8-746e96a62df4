import {Stack, StackProps} from "aws-cdk-lib";
import {CloudfrontS3} from "./cloudfront-s3";
import {ARecord, HostedZone, IHostedZone, RecordTarget} from "aws-cdk-lib/aws-route53";
import {CloudFrontTarget} from "aws-cdk-lib/aws-route53-targets";
import {Construct} from "constructs";
import { Repository } from "aws-cdk-lib/aws-codecommit";

interface AppStackProps extends StackProps {
    bucketName: string
    domainNames: string[]
    certificateArn: string
    zoneName: string
    hostedZoneId: string

}

export class AppStack extends Stack {
    constructor(scope: Construct, id: string, props: AppStackProps) {
        super(scope, id, props);



        const cfS3 = new CloudfrontS3(this, 'cfS3', {
            bucketName: props.bucketName,
            domainNames: props.domainNames,
            certificateArn: props.certificateArn
        })


        const zone = HostedZone.fromHostedZoneAttributes(this, 'zone', {
            zoneName: props.zoneName,
            hostedZoneId: props.hostedZoneId,
        });


        // Route53 add A record.
        for (let domainName of props.domainNames) {
            new ARecord(this, `A_${domainName}`, {
                recordName: domainName,
                target: RecordTarget.fromAlias(new CloudFrontTarget(cfS3.distribution)),
                zone,
            });
        }
    }
}