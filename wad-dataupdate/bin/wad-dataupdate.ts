#!/usr/bin/env node
import 'source-map-support/register';
import * as cdk from 'aws-cdk-lib';
import {AppStack} from '../lib/app-stack';

const app = new cdk.App();
new AppStack(app, 'Test-Wad-Dataupdate', {
    bucketName: "test-slg-wad-client.gorillaapi.com",
    domainNames: ["test-slg-wad-client.gorillaapi.com"],
    certificateArn: "arn:aws:acm:us-east-1:083084509737:certificate/a64676f9-b18f-452c-a083-b66afa650323",
    zoneName: 'gorillaapi.com',
    hostedZoneId: 'Z06028281QU680O4NR3U',
});


